<?php
// Archivo de prueba directo para la base de datos
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Prueba Directa de Base de Datos</h2>";

// Incluir configuración
require_once 'config.php';

$envConfig = EnvironmentConfig::getInstance();
echo "Entorno detectado: " . $envConfig->getEnvironment() . "<br>";

// Configurar parámetros según el entorno
if ($envConfig->isDevelopment()) {
    $host = 'localhost';
    $port = 3306;
    $dbname = 'gestar_db';
    $username = 'root';
    $password = '';
    echo "✅ Usando configuración de DESARROLLO<br>";
} else {
    $host = '*************';
    $port = 3306;
    $dbname = 'gestarse_experian';
    $username = 'gestarse_ncornejo7_experian';
    $password = 'N1c0l7as17';
    echo "✅ Usando configuración de PRODUCCIÓN<br>";
}

echo "Host: $host<br>";
echo "Puerto: $port<br>";
echo "Base de datos: $dbname<br>";
echo "Usuario: $username<br>";
echo "Contraseña: " . (empty($password) ? '(vacía)' : '***') . "<br><br>";

// Intentar conexión
echo "<h3>Intentando Conexión</h3>";

try {
    $mysqli = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($mysqli->connect_error) {
        echo "❌ Error de conexión: " . $mysqli->connect_error . "<br>";
        echo "Código de error: " . $mysqli->connect_errno . "<br>";
    } else {
        echo "✅ Conexión exitosa<br>";
        
        // Probar una consulta simple
        $result = $mysqli->query("SELECT 1 as test");
        if ($result) {
            echo "✅ Consulta de prueba exitosa<br>";
            $row = $result->fetch_assoc();
            echo "Resultado: " . $row['test'] . "<br>";
        } else {
            echo "❌ Error en consulta de prueba: " . $mysqli->error . "<br>";
        }
        
        // Si estamos en desarrollo, intentar crear la base de datos si no existe
        if ($envConfig->isDevelopment()) {
            echo "<h3>Verificando Base de Datos Local</h3>";
            
            // Conectar sin especificar base de datos
            $mysqli_root = new mysqli($host, $username, $password, '', $port);
            if (!$mysqli_root->connect_error) {
                // Verificar si la base de datos existe
                $result = $mysqli_root->query("SHOW DATABASES LIKE '$dbname'");
                if ($result && $result->num_rows > 0) {
                    echo "✅ Base de datos '$dbname' existe<br>";
                } else {
                    echo "❌ Base de datos '$dbname' no existe<br>";
                    echo "Intentando crear la base de datos...<br>";
                    
                    if ($mysqli_root->query("CREATE DATABASE IF NOT EXISTS $dbname")) {
                        echo "✅ Base de datos '$dbname' creada<br>";
                    } else {
                        echo "❌ Error creando base de datos: " . $mysqli_root->error . "<br>";
                    }
                }
                $mysqli_root->close();
            }
        }
        
        $mysqli->close();
    }
    
} catch (Exception $e) {
    echo "❌ Excepción: " . $e->getMessage() . "<br>";
}

// Probar el archivo con_db.php actual
echo "<h3>Probando con_db.php Actual</h3>";
try {
    // Limpiar variables para evitar conflictos
    unset($host, $port, $dbname, $username, $password, $mysqli);
    
    require_once 'con_db.php';
    
    if (isset($conexion)) {
        echo "✅ Variable \$conexion existe<br>";
        echo "Tipo: " . gettype($conexion) . "<br>";
        
        if ($conexion instanceof mysqli) {
            echo "✅ \$conexion es una instancia de mysqli<br>";
            if (!$conexion->connect_error) {
                echo "✅ Conexión activa<br>";
            } else {
                echo "❌ Error en conexión: " . $conexion->connect_error . "<br>";
            }
        } else {
            echo "❌ \$conexion no es una instancia de mysqli<br>";
        }
    } else {
        echo "❌ Variable \$conexion no existe<br>";
    }
    
    if (isset($mysqli)) {
        echo "✅ Variable \$mysqli existe<br>";
    } else {
        echo "❌ Variable \$mysqli no existe<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error incluyendo con_db.php: " . $e->getMessage() . "<br>";
}
?>
