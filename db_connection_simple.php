<?php
// Conexión simple a la base de datos para pruebas
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Incluir configuración
require_once 'config.php';

$envConfig = EnvironmentConfig::getInstance();

// Configuración de base de datos
$host = '*************';
$port = 3306;
$dbname = 'gestarse_experian';
$username = 'gestarse_ncornejo7_experian';
$password = 'N1c0l7as17';

echo "<h2>Conexión Simple a Base de Datos</h2>";
echo "Entorno: " . $envConfig->getEnvironment() . "<br>";
echo "Host: $host<br>";
echo "Base de datos: $dbname<br>";
echo "Usuario: $username<br><br>";

try {
    $conexion = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($conexion->connect_error) {
        echo "❌ Error de conexión: " . $conexion->connect_error . "<br>";
    } else {
        echo "✅ Conexión exitosa<br>";
        
        // Probar consulta simple
        $result = $conexion->query("SELECT 1 as test");
        if ($result) {
            echo "✅ Consulta de prueba exitosa<br>";
        }
        
        // Buscar usuario administrador
        $rut = '12498646-k';
        $query = "SELECT id, nombre_usuario, correo, proyecto FROM tb_experian_usuarios WHERE rut = ?";
        $stmt = $conexion->prepare($query);
        
        if ($stmt) {
            $stmt->bind_param('s', $rut);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                $user = $result->fetch_assoc();
                echo "✅ Usuario administrador encontrado:<br>";
                echo "&nbsp;&nbsp;ID: " . $user['id'] . "<br>";
                echo "&nbsp;&nbsp;Nombre: " . $user['nombre_usuario'] . "<br>";
                echo "&nbsp;&nbsp;Correo: " . $user['correo'] . "<br>";
                echo "&nbsp;&nbsp;Proyecto: " . $user['proyecto'] . "<br>";
                
                // Ahora probar el login completo
                echo "<h3>Prueba de Login Completo</h3>";
                
                // Simular el proceso de login
                $password_input = 'Cocl646k$';
                $query_login = "SELECT id, nombre_usuario, correo, rol, proyecto, password FROM tb_experian_usuarios WHERE rut = ?";
                $stmt_login = $conexion->prepare($query_login);
                
                if ($stmt_login) {
                    $stmt_login->bind_param('s', $rut);
                    $stmt_login->execute();
                    $result_login = $stmt_login->get_result();
                    
                    if ($result_login && $result_login->num_rows > 0) {
                        $user_login = $result_login->fetch_assoc();
                        
                        // Verificar password
                        if (password_verify($password_input, $user_login['password'])) {
                            echo "✅ Password verificado con password_verify()<br>";
                        } elseif ($password_input === $user_login['password']) {
                            echo "✅ Password verificado directamente (sin hash)<br>";
                        } else {
                            echo "❌ Password no coincide<br>";
                            echo "Password ingresado: $password_input<br>";
                            echo "Password en BD: " . substr($user_login['password'], 0, 20) . "...<br>";
                        }
                        
                        echo "Datos completos del usuario:<br>";
                        echo "&nbsp;&nbsp;ID: " . $user_login['id'] . "<br>";
                        echo "&nbsp;&nbsp;Nombre: " . $user_login['nombre_usuario'] . "<br>";
                        echo "&nbsp;&nbsp;Correo: " . $user_login['correo'] . "<br>";
                        echo "&nbsp;&nbsp;Rol: " . $user_login['rol'] . "<br>";
                        echo "&nbsp;&nbsp;Proyecto: " . $user_login['proyecto'] . "<br>";
                        
                    } else {
                        echo "❌ Usuario no encontrado en consulta de login<br>";
                    }
                    $stmt_login->close();
                } else {
                    echo "❌ Error preparando consulta de login: " . $conexion->error . "<br>";
                }
                
            } else {
                echo "❌ Usuario administrador no encontrado<br>";
            }
            $stmt->close();
        } else {
            echo "❌ Error preparando consulta: " . $conexion->error . "<br>";
        }
        
        $conexion->close();
    }
    
} catch (Exception $e) {
    echo "❌ Excepción: " . $e->getMessage() . "<br>";
}
?>
