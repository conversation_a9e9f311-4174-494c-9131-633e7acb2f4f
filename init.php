<?php
// Archivo de inicialización global para todas las páginas
// Se encarga de: definir entorno, aplicar headers y cargar utilidades de cache

// Incluir config primero para definir ENVIRONMENT
require_once __DIR__ . '/config.php';

// Forzar headers de no-cache en desarrollo ANTES de cualquier salida
if (!headers_sent() && defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
    // Invalidar OPcache para el script actual para reflejar cambios al instante
    if (function_exists('opcache_invalidate') && ini_get('opcache.enable')) {
        opcache_invalidate($_SERVER['SCRIPT_FILENAME'], true);
    }

    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
    header("Cache-Control: post-check=0, pre-check=0", false);
    header("Pragma: no-cache");
    header('Expires: 0');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
}

// Cargar utilidades de caché
require_once __DIR__ . '/cache_utils.php';
