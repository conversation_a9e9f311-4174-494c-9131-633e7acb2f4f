<?php
// Explorar la estructura de la tabla de usuarios
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuración de base de datos
$host = '*************';
$port = 3306;
$dbname = 'gestarse_experian';
$username = 'gestarse_ncornejo7_experian';
$password = 'N1c0l7as17';

echo "<h2>Exploración de Tabla de Usuarios</h2>";

try {
    $conexion = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($conexion->connect_error) {
        echo "❌ Error de conexión: " . $conexion->connect_error . "<br>";
    } else {
        echo "✅ Conexión exitosa<br><br>";
        
        // Mostrar estructura de la tabla
        echo "<h3>Estructura de la tabla tb_experian_usuarios</h3>";
        $result = $conexion->query("DESCRIBE tb_experian_usuarios");
        
        if ($result) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th><th>Extra</th></tr>";
            
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table><br>";
        } else {
            echo "❌ Error obteniendo estructura: " . $conexion->error . "<br>";
        }
        
        // Mostrar algunos registros de ejemplo
        echo "<h3>Primeros 5 registros de la tabla</h3>";
        $result = $conexion->query("SELECT * FROM tb_experian_usuarios LIMIT 5");
        
        if ($result) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            
            // Headers
            $fields = $result->fetch_fields();
            echo "<tr>";
            foreach ($fields as $field) {
                echo "<th>" . $field->name . "</th>";
            }
            echo "</tr>";
            
            // Data
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    // Ocultar passwords por seguridad
                    if (strtolower($key) === 'password') {
                        $value = substr($value, 0, 10) . "...";
                    }
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table><br>";
        } else {
            echo "❌ Error obteniendo registros: " . $conexion->error . "<br>";
        }
        
        // Buscar el usuario administrador por diferentes campos posibles
        echo "<h3>Búsqueda del Usuario Administrador</h3>";
        
        $possible_rut_fields = ['rut', 'cedula', 'documento', 'identificacion', 'dni'];
        $found = false;
        
        foreach ($possible_rut_fields as $field) {
            echo "Probando campo '$field'...<br>";
            
            $query = "SELECT * FROM tb_experian_usuarios WHERE $field = '12498646-k' LIMIT 1";
            $result = $conexion->query($query);
            
            if ($result && $result->num_rows > 0) {
                echo "✅ Usuario encontrado usando campo '$field':<br>";
                $user = $result->fetch_assoc();
                
                foreach ($user as $key => $value) {
                    if (strtolower($key) === 'password') {
                        $value = substr($value, 0, 10) . "...";
                    }
                    echo "&nbsp;&nbsp;$key: " . htmlspecialchars($value) . "<br>";
                }
                $found = true;
                break;
            } else {
                echo "&nbsp;&nbsp;No encontrado con '$field'<br>";
            }
        }
        
        if (!$found) {
            echo "❌ Usuario administrador no encontrado con ningún campo probado<br>";
            
            // Mostrar todos los usuarios para ver qué hay
            echo "<h3>Todos los usuarios en la tabla</h3>";
            $result = $conexion->query("SELECT * FROM tb_experian_usuarios");
            
            if ($result) {
                echo "Total de usuarios: " . $result->num_rows . "<br>";
                
                while ($row = $result->fetch_assoc()) {
                    echo "<div style='border: 1px solid #ccc; margin: 5px; padding: 10px;'>";
                    foreach ($row as $key => $value) {
                        if (strtolower($key) === 'password') {
                            $value = substr($value, 0, 10) . "...";
                        }
                        echo "<strong>$key:</strong> " . htmlspecialchars($value) . "<br>";
                    }
                    echo "</div>";
                }
            }
        }
        
        $conexion->close();
    }
    
} catch (Exception $e) {
    echo "❌ Excepción: " . $e->getMessage() . "<br>";
}
?>
