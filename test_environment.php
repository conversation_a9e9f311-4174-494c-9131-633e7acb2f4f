<?php
// Archivo de prueba para verificar la detección de entorno
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Prueba de Detección de Entorno</h2>";

// Información del servidor
echo "<h3>Información del Servidor</h3>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'No definido') . "<br>";
echo "SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? 'No definido') . "<br>";
echo "SERVER_PORT: " . ($_SERVER['SERVER_PORT'] ?? 'No definido') . "<br>";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'No definido') . "<br>";
echo "SCRIPT_FILENAME: " . ($_SERVER['SCRIPT_FILENAME'] ?? 'No definido') . "<br>";

// Probar detección manual
echo "<h3>Detección Manual de Entorno</h3>";

$localIndicators = array(
    'localhost_in_host' => isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'localhost') !== false,
    '127_in_host' => isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false,
    'port_8080' => isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '8080',
    'xampp_in_path' => isset($_SERVER['DOCUMENT_ROOT']) && strpos($_SERVER['DOCUMENT_ROOT'], 'xampp') !== false
);

foreach ($localIndicators as $indicator => $value) {
    echo "$indicator: " . ($value ? '✅ TRUE' : '❌ FALSE') . "<br>";
}

$isDevelopment = in_array(true, $localIndicators, true);
echo "<br><strong>¿Es desarrollo?: " . ($isDevelopment ? '✅ SÍ' : '❌ NO') . "</strong><br>";

// Probar el sistema de configuración
echo "<h3>Sistema de Configuración</h3>";
try {
    require_once 'config.php';
    echo "✅ config.php cargado<br>";
    
    $envConfig = EnvironmentConfig::getInstance();
    echo "✅ EnvironmentConfig instanciado<br>";
    echo "Entorno detectado: " . $envConfig->getEnvironment() . "<br>";
    echo "¿Es desarrollo?: " . ($envConfig->isDevelopment() ? 'SÍ' : 'NO') . "<br>";
    echo "URL Base: " . $envConfig->getBaseUrl() . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Probar configuración de base de datos
echo "<h3>Configuración de Base de Datos</h3>";

if ($isDevelopment) {
    echo "Configuración para DESARROLLO:<br>";
    echo "&nbsp;&nbsp;Host: localhost<br>";
    echo "&nbsp;&nbsp;Puerto: 3306<br>";
    echo "&nbsp;&nbsp;Base de datos: gestar_db<br>";
    echo "&nbsp;&nbsp;Usuario: root<br>";
    echo "&nbsp;&nbsp;Contraseña: (vacía)<br>";
} else {
    echo "Configuración para PRODUCCIÓN:<br>";
    echo "&nbsp;&nbsp;Host: *************<br>";
    echo "&nbsp;&nbsp;Puerto: 3306<br>";
    echo "&nbsp;&nbsp;Base de datos: gestarse_experian<br>";
    echo "&nbsp;&nbsp;Usuario: gestarse_ncornejo7_experian<br>";
    echo "&nbsp;&nbsp;Contraseña: N1c0l7as17<br>";
}
?>
