<?php
// Archivo de prueba para la nueva configuración de base de datos
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Prueba de Nueva Configuración de Base de Datos</h2>";

try {
    require_once 'con_db.php';
    
    echo "✅ con_db.php incluido correctamente<br>";
    
    if (isset($conexion)) {
        echo "✅ Variable \$conexion existe<br>";
        
        if ($conexion instanceof mysqli) {
            echo "✅ \$conexion es una instancia de mysqli<br>";
            
            if (!$conexion->connect_error) {
                echo "✅ Conexión activa<br>";
                
                // Probar consulta simple
                $result = $conexion->query("SELECT 1 as test");
                if ($result) {
                    echo "✅ Consulta de prueba exitosa<br>";
                    $row = $result->fetch_assoc();
                    echo "Resultado: " . $row['test'] . "<br>";
                } else {
                    echo "❌ Error en consulta: " . $conexion->error . "<br>";
                }
                
                // Probar consulta a tabla de usuarios
                $query = "SELECT COUNT(*) as total FROM tb_experian_usuarios";
                $result = $conexion->query($query);
                
                if ($result) {
                    $row = $result->fetch_assoc();
                    echo "✅ Total de usuarios en la base: " . $row['total'] . "<br>";
                } else {
                    echo "❌ Error consultando usuarios: " . $conexion->error . "<br>";
                }
                
                // Buscar el usuario administrador
                $rut = '12498646-k';
                $query = "SELECT id, nombre_usuario, correo, proyecto FROM tb_experian_usuarios WHERE rut = ?";
                $stmt = $conexion->prepare($query);
                
                if ($stmt) {
                    $stmt->bind_param('s', $rut);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result && $result->num_rows > 0) {
                        $user = $result->fetch_assoc();
                        echo "✅ Usuario administrador encontrado:<br>";
                        echo "&nbsp;&nbsp;ID: " . $user['id'] . "<br>";
                        echo "&nbsp;&nbsp;Nombre: " . $user['nombre_usuario'] . "<br>";
                        echo "&nbsp;&nbsp;Correo: " . $user['correo'] . "<br>";
                        echo "&nbsp;&nbsp;Proyecto: " . $user['proyecto'] . "<br>";
                    } else {
                        echo "❌ Usuario administrador no encontrado<br>";
                    }
                    $stmt->close();
                } else {
                    echo "❌ Error preparando consulta: " . $conexion->error . "<br>";
                }
                
            } else {
                echo "❌ Error en conexión: " . $conexion->connect_error . "<br>";
            }
        } else {
            echo "❌ \$conexion no es una instancia de mysqli<br>";
        }
    } else {
        echo "❌ Variable \$conexion no existe<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "Stack trace:<br><pre>" . $e->getTraceAsString() . "</pre>";
}
?>
